{"mcpServers": {"salesforce": {"command": "node", "args": ["mcp_servers/salesforce_server.js"], "type": "process", "description": "A server to query Salesforce for open cases by account name. Exposes a /cases endpoint.", "port": 3000, "tool_schema": {"name": "query_salesforce", "description": "Query Salesforce for open cases by account name. Returns a JSON array of case records.", "parameters": {"type": "object", "properties": {"account_name": {"type": "string", "description": "Name of the Account to search cases for, e.g., 'ACME Corp'"}}, "required": ["account_name"]}}}, "jira": {"command": "node", "args": ["mcp_servers/jira_server.js"], "type": "process", "description": "A server to query JIRA for tickets by project, assignee, status, or summary. Exposes /tickets and /ticket/:key endpoints.", "port": 3001, "tool_schema": {"name": "query_jira_tickets", "description": "Query JIRA for tickets based on project, assignee, status, or summary. Returns a JSON array of ticket records.", "parameters": {"type": "object", "properties": {"project": {"type": "string", "description": "Project key to search in, e.g., 'PROJ'"}, "assignee": {"type": "string", "description": "Email or username of the assignee"}, "status": {"type": "string", "description": "Status of the tickets, e.g., 'In Progress', 'Open'"}, "summary": {"type": "string", "description": "Text to search for in ticket summaries"}}}}}, "playwright_check": {"command": "node", "args": ["mcp_servers/playwright_check.js"], "type": "command", "description": "A script to check if the Contact Us form on a given URL loads properly and is present.", "tool_schema": {"name": "check_contact_form", "description": "Check if the Contact Us form on a given URL loads properly and is present. Returns a status (PASS/FAIL/ERROR) and message.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the Contact Us page to check, e.g., 'https://www.example.com/contact'"}}, "required": ["url"]}}}}}