<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Year_Built_Not_Future</fullName>
    <active>true</active>
    <description>Year Built must not be greater than the current year and must be a valid 4-digit year</description>
    <errorConditionFormula>AND(
    NOT(ISBLANK(Year_Built__c)),
    OR(
        Year_Built__c &gt; YEAR(TODAY()),
        Year_Built__c &lt; 1000,
        Year_Built__c &gt; 9999
    )
)</errorConditionFormula>
    <errorDisplayField>Year_Built__c</errorDisplayField>
    <errorMessage>Year Built must be a valid 4-digit year and cannot be in the future.</errorMessage>
</ValidationRule>
