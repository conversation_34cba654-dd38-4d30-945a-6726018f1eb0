// mcp_servers/jira_test.js
import dotenv from 'dotenv';

dotenv.config();

// Simple test to verify JIRA configuration
async function testJiraConnection() {
  const JIRA_BASE_URL = process.env.JIRA_BASE_URL;
  const JIRA_EMAIL = process.env.JIRA_EMAIL;
  const JIRA_API_TOKEN = process.env.JIRA_API_TOKEN;

  if (!JIRA_BASE_URL || !JIRA_EMAIL || !JIRA_API_TOKEN) {
    console.log('❌ JIRA configuration incomplete. Please check your .env file:');
    console.log(`JIRA_BASE_URL: ${JIRA_BASE_URL ? '✅ Set' : '❌ Missing'}`);
    console.log(`JIRA_EMAIL: ${JIRA_EMAIL ? '✅ Set' : '❌ Missing'}`);
    console.log(`JIRA_API_TOKEN: ${JIRA_API_TOKEN ? '✅ Set' : '❌ Missing'}`);
    return;
  }

  console.log('🔍 Testing JIRA connection...');
  console.log(`JIRA URL: ${JIRA_BASE_URL}`);
  console.log(`JIRA Email: ${JIRA_EMAIL}`);

  const auth = Buffer.from(`${JIRA_EMAIL}:${JIRA_API_TOKEN}`).toString('base64');
  
  try {
    const response = await fetch(`${JIRA_BASE_URL}/rest/api/3/serverInfo`, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ JIRA connection successful!');
      console.log(`Server Title: ${data.serverTitle}`);
      console.log(`Version: ${data.version}`);
    } else {
      console.log('❌ JIRA connection failed:', response.status, response.statusText);
      console.log('Please check your credentials and JIRA URL.');
    }
  } catch (error) {
    console.log('❌ JIRA connection error:', error.message);
  }
}

testJiraConnection();
