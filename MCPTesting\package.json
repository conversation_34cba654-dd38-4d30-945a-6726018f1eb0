{"name": "salesforce-app", "private": true, "version": "1.0.0", "description": "Salesforce App", "scripts": {"lint": "eslint **/{aura,lwc}/**/*.js", "test": "npm run test:unit", "test:unit": "sfdx-lwc-jest", "test:unit:watch": "sfdx-lwc-jest --watch", "test:unit:debug": "sfdx-lwc-jest --debug", "test:unit:coverage": "sfdx-lwc-jest --coverage", "prettier": "prettier --write \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prettier:verify": "prettier --check \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prepare": "husky || true", "precommit": "lint-staged"}, "devDependencies": {"@lwc/eslint-plugin-lwc": "^3.1.0", "@prettier/plugin-xml": "^3.4.1", "@salesforce/eslint-config-lwc": "^4.0.0", "@salesforce/eslint-plugin-aura": "^3.0.0", "@salesforce/eslint-plugin-lightning": "^2.0.0", "@salesforce/sfdx-lwc-jest": "^7.0.2", "eslint": "^9.29.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.14.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "prettier-plugin-apex": "^2.2.6"}, "lint-staged": {"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}": ["prettier --write"], "**/{aura,lwc}/**/*.js": ["eslint"], "**/lwc/**": ["sfdx-lwc-jest -- --bail --findRelatedTests --passWithNoTests"]}}