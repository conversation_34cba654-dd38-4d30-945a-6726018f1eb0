import express from 'express';
import { Connection } from 'jsforce';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env

const app = express();
const PORT = process.env.SALESFORCE_MCP_PORT || 3000; // Use a dedicated port for Salesforce MCP

// Salesforce connection setup
const conn = new Connection({
  loginUrl: process.env.SF_LOGIN_URL || 'https://login.salesforce.com'
});

// Authenticate to Salesforce on server start
async function connectToSalesforce() {
  try {
    await conn.login(
      process.env.SALESFORCE_USERNAME,
      process.env.SALESFORCE_PASSWORD + process.env.SALESFORCE_SECURITY_TOKEN
    );
    console.log("Salesforce MCP Server: Logged in as " + conn.userInfo.id);
  } catch (err) {
    console.error("Salesforce MCP Server: Initial login error:", err.message);
    // Exit if unable to connect, or implement retry logic
    process.exit(1);
  }
}

// Middleware to ensure Salesforce connection is alive (optional, for long-running servers)
app.use(async (req, res, next) => {
  if (!conn.accessToken) {
    console.log("Salesforce MCP Server: Re-authenticating to Salesforce...");
    try {
      await connectToSalesforce();
      next();
    } catch (err) {
      res.status(503).send({ error: "Salesforce connection unavailable." });
    }
  } else {
    next();
  }
});

// Query endpoint for open cases
app.get('/cases', async (req, res) => {
  try {
    const account = req.query.account;
    if (!account) {
      return res.status(400).send({ error: "account query parameter is required" });
    }

    // SOQL query to find open cases for the given Account name
    const soql = `SELECT Id, CaseNumber, Status, Subject FROM Case WHERE Status = 'Open' AND Account.Name = '${account.replace(/'/g, "\\'")}'`;
    const result = await conn.query(soql);

    console.log(`Salesforce Query: SOQL for '${account}' returned ${result.totalSize} records.`);
    return res.json(result.records);
  } catch (err) {
    console.error("Salesforce Query Error:", err);
    res.status(500).send({ error: err.message });
  }
});

// Health check endpoint
app.get('/healthz', (req, res) => {
  if (conn.accessToken) {
    res.status(200).send('OK');
  } else {
    res.status(500).send('Salesforce not connected');
  }
});

// Start the server after connecting to Salesforce
connectToSalesforce().then(() => {
  app.listen(PORT, () => console.log(`Salesforce MCP Server running on port ${PORT}`));
}); 