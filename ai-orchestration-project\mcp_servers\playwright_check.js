import { chromium } from 'playwright';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env

async function checkContactForm(url) {
  let browser;
  try {
    // Basic URL validation to prevent arbitrary navigation for security
    const allowedDomains = [new URL(process.env.CONTACT_US_URL).hostname];
    const parsedUrl = new URL(url);
    if (!allowedDomains.includes(parsedUrl.hostname)) {
      throw new Error(`Navigation to '${url}' is not allowed. Only allowed domains: ${allowedDomains.join(', ')}.`);
    }

    browser = await chromium.launch({ headless: true }); // Run in headless mode for server environments
    const page = await browser.newPage();

    console.log(`Playwright Check: Navigating to ${url}`);
    const response = await page.goto(url, { waitUntil: 'load', timeout: 15000 }); // Increased timeout for potentially slow pages

    if (!response || !response.ok()) {
      throw new Error(`Failed to load page, HTTP status=${response ? response.status() : 'No Response'}`);
    }

    // Check if a form element is present.
    const formSelector = 'form';
    const formElem = await page.$(formSelector);

    if (formElem) {
      console.log("Playwright Check: Contact form found on page.");
      return { status: "PASS", message: "Contact Us form loaded successfully." };
    } else {
      console.log("Playwright Check: Contact form NOT found on page.");
      return { status: "FAIL", message: "Contact Us form not present on page." };
    }
  } catch (err) {
    console.error("Playwright Check Error:", err);
    return { status: "ERROR", message: err.message };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// This block allows the script to be run directly from the command line for testing
const targetUrl = process.argv[2] || process.env.CONTACT_US_URL || "https://www.example.com/contact";
checkContactForm(targetUrl).then(result => {
  console.log("Playwright Result:", JSON.stringify(result, null, 2));
  process.exit(result.status === "PASS" ? 0 : 1); // Exit with 0 for success, 1 for failure
}); 