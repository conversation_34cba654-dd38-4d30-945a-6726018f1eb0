<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Number_of_Floors_Positive</fullName>
    <active>true</active>
    <description>Number of Floors must be a positive integer</description>
    <errorConditionFormula>AND(
    NOT(ISBLANK(Number_of_Floors__c)),
    Number_of_Floors__c &lt;= 0
)</errorConditionFormula>
    <errorDisplayField>Number_of_Floors__c</errorDisplayField>
    <errorMessage>Number of Floors must be greater than 0.</errorMessage>
</ValidationRule>
