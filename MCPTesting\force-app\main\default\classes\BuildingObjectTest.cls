/**
 * @description Test class for Building__c object functionality
 * <AUTHOR> Development Bot
 * @date 2025-08-02
 */
@isTest
public class BuildingObjectTest {
    
    /**
     * @description Test data factory for creating test accounts
     */
    @TestSetup
    static void makeData() {
        // Create test Account
        Account testAccount = new Account(
            Name = 'Test Property Company',
            Type = 'Customer'
        );
        insert testAccount;
    }
    
    /**
     * @description Test successful creation of Building record with valid data
     */
    @isTest
    static void testBuildingCreationSuccess() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Test Office Building',
            Address__c = '123 Main Street, City, State 12345',
            Account__c = testAccount.Id,
            Number_of_Floors__c = 15,
            Year_Built__c = 2020,
            Building_Type__c = 'Office'
        );
        
        Test.startTest();
        insert testBuilding;
        Test.stopTest();
        
        // Verify building was created successfully
        Building__c insertedBuilding = [
            SELECT Id, Building_Name__c, Address__c, Account__c, 
                   Number_of_Floors__c, Year_Built__c, Building_Type__c
            FROM Building__c 
            WHERE Id = :testBuilding.Id
        ];
        
        System.assertEquals('Test Office Building', insertedBuilding.Building_Name__c);
        System.assertEquals('123 Main Street, City, State 12345', insertedBuilding.Address__c);
        System.assertEquals(testAccount.Id, insertedBuilding.Account__c);
        System.assertEquals(15, insertedBuilding.Number_of_Floors__c);
        System.assertEquals(2020, insertedBuilding.Year_Built__c);
        System.assertEquals('Office', insertedBuilding.Building_Type__c);
    }
    
    /**
     * @description Test building creation with required fields only
     */
    @isTest
    static void testBuildingCreationRequiredFieldsOnly() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record with only required fields
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Minimal Building',
            Address__c = '456 Oak Avenue, Town, State 67890',
            Account__c = testAccount.Id
        );
        
        Test.startTest();
        insert testBuilding;
        Test.stopTest();
        
        // Verify building was created successfully
        Building__c insertedBuilding = [
            SELECT Id, Building_Name__c, Address__c, Account__c
            FROM Building__c 
            WHERE Id = :testBuilding.Id
        ];
        
        System.assertEquals('Minimal Building', insertedBuilding.Building_Name__c);
        System.assertEquals('456 Oak Avenue, Town, State 67890', insertedBuilding.Address__c);
        System.assertEquals(testAccount.Id, insertedBuilding.Account__c);
    }
    
    /**
     * @description Test validation rule for Year Built not in future
     */
    @isTest
    static void testYearBuiltValidationFuture() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record with future year
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Future Building',
            Address__c = '789 Future Lane, Tomorrow, State 99999',
            Account__c = testAccount.Id,
            Year_Built__c = System.today().year() + 10
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for future year');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Year Built must be a valid 4-digit year and cannot be in the future'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test validation rule for Year Built invalid format
     */
    @isTest
    static void testYearBuiltValidationInvalidFormat() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record with invalid year (too small)
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Invalid Year Building',
            Address__c = '321 Invalid Street, Error, State 11111',
            Account__c = testAccount.Id,
            Year_Built__c = 99  // Invalid 2-digit year
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for invalid year format');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Year Built must be a valid 4-digit year and cannot be in the future'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test validation rule for Number of Floors positive value
     */
    @isTest
    static void testNumberOfFloorsValidationNegative() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record with negative floors
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Underground Building',
            Address__c = '987 Basement Blvd, Underground, State 22222',
            Account__c = testAccount.Id,
            Number_of_Floors__c = -5
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for negative floors');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Number of Floors must be greater than 0'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test validation rule for Number of Floors zero value
     */
    @isTest
    static void testNumberOfFloorsValidationZero() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record with zero floors
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'No Floor Building',
            Address__c = '555 Empty Lot, Nowhere, State 33333',
            Account__c = testAccount.Id,
            Number_of_Floors__c = 0
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for zero floors');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Number of Floors must be greater than 0'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test required field validation for Building Name
     */
    @isTest
    static void testRequiredFieldBuildingName() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record without Building Name
        Building__c testBuilding = new Building__c(
            Address__c = '777 Required Fields Ave, Validation, State 44444',
            Account__c = testAccount.Id
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for missing Building Name');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Required fields are missing'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test required field validation for Address
     */
    @isTest
    static void testRequiredFieldAddress() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create Building record without Address
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'No Address Building',
            Account__c = testAccount.Id
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for missing Address');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Required fields are missing'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test required field validation for Account
     */
    @isTest
    static void testRequiredFieldAccount() {
        // Create Building record without Account
        Building__c testBuilding = new Building__c(
            Building_Name__c = 'Orphan Building',
            Address__c = '888 No Owner Street, Orphaned, State 55555'
        );
        
        Test.startTest();
        try {
            insert testBuilding;
            System.assert(false, 'Expected validation error for missing Account');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Required fields are missing'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test bulk insert of Building records
     */
    @isTest
    static void testBulkBuildingInsert() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // Create multiple Building records
        List<Building__c> buildingList = new List<Building__c>();
        for (Integer i = 1; i <= 200; i++) {
            buildingList.add(new Building__c(
                Building_Name__c = 'Bulk Building ' + i,
                Address__c = i + ' Bulk Street, Mass, State 66666',
                Account__c = testAccount.Id,
                Number_of_Floors__c = Math.mod(i, 50) + 1,
                Year_Built__c = 1950 + Math.mod(i, 70),
                Building_Type__c = Math.mod(i, 2) == 0 ? 'Office' : 'Retail'
            ));
        }
        
        Test.startTest();
        insert buildingList;
        Test.stopTest();
        
        // Verify all buildings were created
        List<Building__c> insertedBuildings = [
            SELECT Id FROM Building__c WHERE Account__c = :testAccount.Id
        ];
        System.assertEquals(200, insertedBuildings.size());
    }
    
    /**
     * @description Test all picklist values for Building Type
     */
    @isTest
    static void testBuildingTypePicklistValues() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        List<String> buildingTypes = new List<String>{'Office', 'Retail', 'Residential', 'Industrial', 'Other'};
        List<Building__c> buildingList = new List<Building__c>();
        
        for (String buildingType : buildingTypes) {
            buildingList.add(new Building__c(
                Building_Name__c = buildingType + ' Building',
                Address__c = '100 ' + buildingType + ' Street, Type, State 77777',
                Account__c = testAccount.Id,
                Building_Type__c = buildingType
            ));
        }
        
        Test.startTest();
        insert buildingList;
        Test.stopTest();
        
        // Verify all building types were created successfully
        List<Building__c> insertedBuildings = [
            SELECT Id, Building_Type__c FROM Building__c 
            WHERE Account__c = :testAccount.Id AND Building_Type__c != null
        ];
        System.assertEquals(5, insertedBuildings.size());
    }
}
