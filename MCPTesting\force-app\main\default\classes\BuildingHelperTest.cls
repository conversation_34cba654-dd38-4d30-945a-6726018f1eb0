/**
 * @description Test class for BuildingHelper utility methods
 * <AUTHOR> Development Bot
 * @date 2025-08-02
 */
@isTest
public class BuildingHelperTest {
    
    /**
     * @description Test data setup
     */
    @TestSetup
    static void makeData() {
        // Create test Account
        Account testAccount = new Account(
            Name = 'Test Property Management Company',
            Type = 'Customer'
        );
        insert testAccount;
        
        // Create test Buildings
        List<Building__c> testBuildings = new List<Building__c>();
        testBuildings.add(new Building__c(
            Building_Name__c = 'Office Tower A',
            Address__c = '100 Business Ave, Downtown, State 12345',
            Account__c = testAccount.Id,
            Number_of_Floors__c = 25,
            Year_Built__c = 2015,
            Building_Type__c = 'Office'
        ));
        testBuildings.add(new Building__c(
            Building_Name__c = 'Retail Plaza B',
            Address__c = '200 Shopping Blvd, Mall District, State 12345',
            Account__c = testAccount.Id,
            Number_of_Floors__c = 3,
            Year_Built__c = 2010,
            Building_Type__c = 'Retail'
        ));
        testBuildings.add(new Building__c(
            Building_Name__c = 'Residential Complex C',
            Address__c = '300 Living Lane, Suburbs, State 12345',
            Account__c = testAccount.Id,
            Number_of_Floors__c = 12,
            Year_Built__c = 2020,
            Building_Type__c = 'Residential'
        ));
        insert testBuildings;
    }
    
    /**
     * @description Test getBuildingsByAccount method
     */
    @isTest
    static void testGetBuildingsByAccount() {
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        Test.startTest();
        List<Building__c> buildings = BuildingHelper.getBuildingsByAccount(testAccount.Id);
        Test.stopTest();
        
        System.assertEquals(3, buildings.size(), 'Should return 3 buildings for the test account');
        System.assertEquals('Office Tower A', buildings[0].Building_Name__c, 'Buildings should be ordered by name');
    }
    
    /**
     * @description Test getBuildingsByAccount with null input
     */
    @isTest
    static void testGetBuildingsByAccountNull() {
        Test.startTest();
        try {
            List<Building__c> buildings = BuildingHelper.getBuildingsByAccount(null);
            System.assert(false, 'Should throw exception for null account ID');
        } catch (IllegalArgumentException e) {
            System.assert(e.getMessage().contains('Account ID cannot be null'));
        }
        Test.stopTest();
    }
    
    /**
     * @description Test getBuildingsByType method
     */
    @isTest
    static void testGetBuildingsByType() {
        Test.startTest();
        List<Building__c> officeBuildings = BuildingHelper.getBuildingsByType('Office');
        List<Building__c> retailBuildings = BuildingHelper.getBuildingsByType('Retail');
        List<Building__c> residentialBuildings = BuildingHelper.getBuildingsByType('Residential');
        Test.stopTest();
        
        System.assertEquals(1, officeBuildings.size(), 'Should return 1 office building');
        System.assertEquals(1, retailBuildings.size(), 'Should return 1 retail building');
        System.assertEquals(1, residentialBuildings.size(), 'Should return 1 residential building');
        System.assertEquals('Office Tower A', officeBuildings[0].Building_Name__c);
        System.assertEquals('Retail Plaza B', retailBuildings[0].Building_Name__c);
        System.assertEquals('Residential Complex C', residentialBuildings[0].Building_Name__c);
    }
    
    /**
     * @description Test getBuildingsByType with blank input
     */
    @isTest
    static void testGetBuildingsByTypeBlank() {
        Test.startTest();
        List<Building__c> buildings = BuildingHelper.getBuildingsByType('');
        Test.stopTest();
        
        System.assertEquals(0, buildings.size(), 'Should return empty list for blank building type');
    }
    
    /**
     * @description Test getTotalFloorsForAccount method
     */
    @isTest
    static void testGetTotalFloorsForAccount() {
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        Test.startTest();
        Integer totalFloors = BuildingHelper.getTotalFloorsForAccount(testAccount.Id);
        Test.stopTest();
        
        System.assertEquals(40, totalFloors, 'Total floors should be 25 + 3 + 12 = 40');
    }
    
    /**
     * @description Test getTotalFloorsForAccount with null input
     */
    @isTest
    static void testGetTotalFloorsForAccountNull() {
        Test.startTest();
        Integer totalFloors = BuildingHelper.getTotalFloorsForAccount(null);
        Test.stopTest();
        
        System.assertEquals(0, totalFloors, 'Should return 0 for null account ID');
    }
    
    /**
     * @description Test getBuildingCountByType method
     */
    @isTest
    static void testGetBuildingCountByType() {
        Test.startTest();
        Map<String, Integer> buildingStats = BuildingHelper.getBuildingCountByType();
        Test.stopTest();
        
        System.assertEquals(3, buildingStats.size(), 'Should have stats for 3 building types');
        System.assertEquals(1, buildingStats.get('Office'), 'Should have 1 office building');
        System.assertEquals(1, buildingStats.get('Retail'), 'Should have 1 retail building');
        System.assertEquals(1, buildingStats.get('Residential'), 'Should have 1 residential building');
    }
    
    /**
     * @description Test validateBuildings with valid data
     */
    @isTest
    static void testValidateBuildingsValid() {
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        List<Building__c> buildings = [SELECT Id, Building_Name__c, Address__c, Account__c, 
                                       Number_of_Floors__c, Year_Built__c FROM Building__c];
        
        Test.startTest();
        List<String> errors = BuildingHelper.validateBuildings(buildings);
        Test.stopTest();
        
        System.assertEquals(0, errors.size(), 'Should have no validation errors for valid buildings');
    }
    
    /**
     * @description Test validateBuildings with invalid data
     */
    @isTest
    static void testValidateBuildingsInvalid() {
        List<Building__c> invalidBuildings = new List<Building__c>();
        invalidBuildings.add(new Building__c(
            Building_Name__c = '', // Missing required field
            Address__c = 'Valid Address',
            Account__c = null, // Missing required field
            Number_of_Floors__c = -5, // Invalid value
            Year_Built__c = 2050 // Future year
        ));
        
        Test.startTest();
        List<String> errors = BuildingHelper.validateBuildings(invalidBuildings);
        Test.stopTest();
        
        System.assert(errors.size() > 0, 'Should have validation errors for invalid buildings');
        System.assert(errors[0].contains('Building Name is required'), 'Should validate Building Name');
    }
    
    /**
     * @description Test validateBuildings with null input
     */
    @isTest
    static void testValidateBuildingsNull() {
        Test.startTest();
        List<String> errors = BuildingHelper.validateBuildings(null);
        Test.stopTest();
        
        System.assertEquals(1, errors.size(), 'Should have 1 error for null input');
        System.assert(errors[0].contains('Building list cannot be null or empty'));
    }
    
    /**
     * @description Test createSampleBuildings method
     */
    @isTest
    static void testCreateSampleBuildings() {
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        Test.startTest();
        List<Building__c> sampleBuildings = BuildingHelper.createSampleBuildings(testAccount.Id, 5);
        Test.stopTest();
        
        System.assertEquals(5, sampleBuildings.size(), 'Should create 5 sample buildings');
        
        for (Building__c building : sampleBuildings) {
            System.assertNotEquals(null, building.Building_Name__c, 'Building Name should be populated');
            System.assertNotEquals(null, building.Address__c, 'Address should be populated');
            System.assertEquals(testAccount.Id, building.Account__c, 'Account should be set correctly');
            System.assert(building.Number_of_Floors__c > 0, 'Number of floors should be positive');
            System.assert(building.Year_Built__c >= 1980, 'Year built should be realistic');
        }
    }
    
    /**
     * @description Test createSampleBuildings with invalid input
     */
    @isTest
    static void testCreateSampleBuildingsInvalid() {
        Test.startTest();
        List<Building__c> emptyResult1 = BuildingHelper.createSampleBuildings(null, 5);
        List<Building__c> emptyResult2 = BuildingHelper.createSampleBuildings([SELECT Id FROM Account LIMIT 1].Id, 0);
        Test.stopTest();
        
        System.assertEquals(0, emptyResult1.size(), 'Should return empty list for null account');
        System.assertEquals(0, emptyResult2.size(), 'Should return empty list for 0 buildings');
    }
    
    /**
     * @description Test all building type constants
     */
    @isTest
    static void testBuildingTypeConstants() {
        System.assertEquals('Office', BuildingHelper.BUILDING_TYPE_OFFICE);
        System.assertEquals('Retail', BuildingHelper.BUILDING_TYPE_RETAIL);
        System.assertEquals('Residential', BuildingHelper.BUILDING_TYPE_RESIDENTIAL);
        System.assertEquals('Industrial', BuildingHelper.BUILDING_TYPE_INDUSTRIAL);
        System.assertEquals('Other', BuildingHelper.BUILDING_TYPE_OTHER);
    }
}
