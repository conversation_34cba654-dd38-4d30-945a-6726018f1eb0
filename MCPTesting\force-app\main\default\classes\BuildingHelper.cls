/**
 * @description Utility class for Building__c object operations
 * <AUTHOR> Development Bot
 * @date 2025-08-02
 */
public with sharing class BuildingHelper {
    
    /**
     * @description Constants for Building Type picklist values
     */
    public static final String BUILDING_TYPE_OFFICE = 'Office';
    public static final String BUILDING_TYPE_RETAIL = 'Retail';
    public static final String BUILDING_TYPE_RESIDENTIAL = 'Residential';
    public static final String BUILDING_TYPE_INDUSTRIAL = 'Industrial';
    public static final String BUILDING_TYPE_OTHER = 'Other';
    
    /**
     * @description Get all Building records for a given Account
     * @param accountId The Account ID to query buildings for
     * @return List of Building__c records
     */
    public static List<Building__c> getBuildingsByAccount(Id accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException('Account ID cannot be null');
        }
        
        return [
            SELECT Id, Name, Building_Name__c, Address__c, Account__c,
                   Number_of_Floors__c, Year_Built__c, Building_Type__c,
                   CreatedDate, LastModifiedDate
            FROM Building__c
            WHERE Account__c = :accountId
            ORDER BY Building_Name__c ASC
        ];
    }
    
    /**
     * @description Get Building records by Type
     * @param buildingType The building type to filter by
     * @return List of Building__c records
     */
    public static List<Building__c> getBuildingsByType(String buildingType) {
        if (String.isBlank(buildingType)) {
            return new List<Building__c>();
        }
        
        return [
            SELECT Id, Name, Building_Name__c, Address__c, Account__c,
                   Number_of_Floors__c, Year_Built__c, Building_Type__c
            FROM Building__c
            WHERE Building_Type__c = :buildingType
            ORDER BY Building_Name__c ASC
        ];
    }
    
    /**
     * @description Calculate total floor space across buildings for an Account
     * @param accountId The Account ID to calculate for
     * @return Integer total number of floors
     */
    public static Integer getTotalFloorsForAccount(Id accountId) {
        if (accountId == null) {
            return 0;
        }
        
        AggregateResult[] results = [
            SELECT SUM(Number_of_Floors__c) totalFloors
            FROM Building__c
            WHERE Account__c = :accountId
            AND Number_of_Floors__c != null
        ];
        
        if (results.size() > 0 && results[0].get('totalFloors') != null) {
            Decimal total = (Decimal)results[0].get('totalFloors');
            return total.intValue();
        }
        
        return 0;
    }
    
    /**
     * @description Get Building statistics by type for reporting
     * @return Map of building type to count
     */
    public static Map<String, Integer> getBuildingCountByType() {
        Map<String, Integer> buildingStats = new Map<String, Integer>();
        
        AggregateResult[] results = [
            SELECT Building_Type__c buildingType, COUNT(Id) buildingCount
            FROM Building__c
            WHERE Building_Type__c != null
            GROUP BY Building_Type__c
        ];
        
        for (AggregateResult result : results) {
            String buildingType = (String)result.get('buildingType');
            Integer count = (Integer)result.get('buildingCount');
            buildingStats.put(buildingType, count);
        }
        
        return buildingStats;
    }
    
    /**
     * @description Validate Building records before DML operations
     * @param buildings List of Building__c records to validate
     * @return List of validation error messages
     */
    public static List<String> validateBuildings(List<Building__c> buildings) {
        List<String> errors = new List<String>();
        
        if (buildings == null || buildings.isEmpty()) {
            errors.add('Building list cannot be null or empty');
            return errors;
        }
        
        for (Building__c building : buildings) {
            // Validate required fields
            if (String.isBlank(building.Building_Name__c)) {
                errors.add('Building Name is required for record: ' + building.Id);
            }
            
            if (String.isBlank(building.Address__c)) {
                errors.add('Address is required for record: ' + building.Id);
            }
            
            if (building.Account__c == null) {
                errors.add('Account is required for record: ' + building.Id);
            }
            
            // Validate Year Built
            if (building.Year_Built__c != null) {
                Integer currentYear = Date.today().year();
                if (building.Year_Built__c > currentYear) {
                    errors.add('Year Built cannot be in the future for record: ' + building.Id);
                }
                if (building.Year_Built__c < 1000 || building.Year_Built__c > 9999) {
                    errors.add('Year Built must be a 4-digit year for record: ' + building.Id);
                }
            }
            
            // Validate Number of Floors
            if (building.Number_of_Floors__c != null && building.Number_of_Floors__c <= 0) {
                errors.add('Number of Floors must be greater than 0 for record: ' + building.Id);
            }
        }
        
        return errors;
    }
    
    /**
     * @description Create sample Building data for testing/demo purposes
     * @param accountId The Account ID to associate buildings with
     * @param numberOfBuildings Number of sample buildings to create
     * @return List of created Building__c records
     */
    public static List<Building__c> createSampleBuildings(Id accountId, Integer numberOfBuildings) {
        if (accountId == null || numberOfBuildings <= 0) {
            return new List<Building__c>();
        }
        
        List<Building__c> sampleBuildings = new List<Building__c>();
        List<String> buildingTypes = new List<String>{
            BUILDING_TYPE_OFFICE, BUILDING_TYPE_RETAIL, BUILDING_TYPE_RESIDENTIAL,
            BUILDING_TYPE_INDUSTRIAL, BUILDING_TYPE_OTHER
        };
        
        for (Integer i = 1; i <= numberOfBuildings; i++) {
            Building__c building = new Building__c(
                Building_Name__c = 'Sample Building ' + i,
                Address__c = i + ' Sample Street, Demo City, State 12345',
                Account__c = accountId,
                Number_of_Floors__c = Math.mod(i, 20) + 1,
                Year_Built__c = 1980 + Math.mod(i, 40),
                Building_Type__c = buildingTypes[Math.mod(i - 1, buildingTypes.size())]
            );
            sampleBuildings.add(building);
        }
        
        return sampleBuildings;
    }
}
