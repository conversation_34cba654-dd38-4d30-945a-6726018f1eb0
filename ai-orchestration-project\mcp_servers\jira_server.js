// mcp_servers/jira_server.js
import express from 'express';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env

const app = express();
const PORT = process.env.JIRA_MCP_PORT || 3001; // Use a dedicated port for JIRA MCP

app.use(express.json()); // Parse JSON bodies

// JIRA API configuration
const JIRA_BASE_URL = process.env.JIRA_BASE_URL; // e.g., https://yourcompany.atlassian.net
const JIRA_EMAIL = process.env.JIRA_EMAIL;
const JIRA_API_TOKEN = process.env.JIRA_API_TOKEN;

// Basic auth header for JIRA API
const getAuthHeader = () => {
  const auth = Buffer.from(`${JIRA_EMAIL}:${JIRA_API_TOKEN}`).toString('base64');
  return `Basic ${auth}`;
};

// Helper function to make JIRA API requests
async function makeJiraRequest(endpoint, method = 'GET', body = null) {
  const url = `${JIRA_BASE_URL}/rest/api/3${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': getAuthHeader(),
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('JIRA API Request Error:', error);
    throw error;
  }
}

// Endpoint to search for JIRA tickets
app.get('/tickets', async (req, res) => {
  try {
    const { project, assignee, status, summary } = req.query;
    
    // Build JQL query based on parameters
    let jql = '';
    const conditions = [];
    
    if (project) {
      conditions.push(`project = "${project}"`);
    }
    
    if (assignee) {
      conditions.push(`assignee = "${assignee}"`);
    }
    
    if (status) {
      conditions.push(`status = "${status}"`);
    }
    
    if (summary) {
      conditions.push(`summary ~ "${summary}"`);
    }
    
    // Default to open issues if no status specified
    if (!status) {
      conditions.push('status != "Done" AND status != "Closed"');
    }
    
    jql = conditions.join(' AND ');
    
    if (!jql) {
      jql = 'status != "Done" AND status != "Closed"'; // Default query for open tickets
    }
    
    console.log(`JIRA Query: JQL = ${jql}`);
    
    const result = await makeJiraRequest(`/search?jql=${encodeURIComponent(jql)}&fields=key,summary,status,assignee,priority,created,updated`);
    
    // Format the response to be more readable
    const formattedIssues = result.issues.map(issue => ({
      key: issue.key,
      summary: issue.fields.summary,
      status: issue.fields.status.name,
      assignee: issue.fields.assignee ? issue.fields.assignee.displayName : 'Unassigned',
      priority: issue.fields.priority ? issue.fields.priority.name : 'None',
      created: issue.fields.created,
      updated: issue.fields.updated
    }));
    
    console.log(`JIRA Query: Found ${formattedIssues.length} tickets.`);
    return res.json({
      total: result.total,
      tickets: formattedIssues
    });
    
  } catch (err) {
    console.error("JIRA Query Error:", err);
    res.status(500).send({ error: err.message });
  }
});

// Endpoint to get a specific JIRA ticket by key
app.get('/ticket/:key', async (req, res) => {
  try {
    const { key } = req.params;
    
    console.log(`JIRA Query: Getting ticket ${key}`);
    
    const result = await makeJiraRequest(`/issue/${key}?fields=key,summary,description,status,assignee,priority,created,updated,reporter`);
    
    // Format the response
    const formattedTicket = {
      key: result.key,
      summary: result.fields.summary,
      description: result.fields.description ? result.fields.description.content : 'No description',
      status: result.fields.status.name,
      assignee: result.fields.assignee ? result.fields.assignee.displayName : 'Unassigned',
      reporter: result.fields.reporter ? result.fields.reporter.displayName : 'Unknown',
      priority: result.fields.priority ? result.fields.priority.name : 'None',
      created: result.fields.created,
      updated: result.fields.updated
    };
    
    console.log(`JIRA Query: Retrieved ticket ${key}`);
    return res.json(formattedTicket);
    
  } catch (err) {
    console.error("JIRA Ticket Retrieval Error:", err);
    res.status(500).send({ error: err.message });
  }
});

// Endpoint to add a comment to a JIRA ticket
app.post('/ticket/:key/comment', async (req, res) => {
  try {
    const { key } = req.params;
    const { comment } = req.body;
    
    if (!comment) {
      return res.status(400).send({ error: "comment field is required in request body" });
    }
    
    console.log(`JIRA Comment: Adding comment to ticket ${key}`);
    
    const commentBody = {
      body: {
        type: "doc",
        version: 1,
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: comment
              }
            ]
          }
        ]
      }
    };
    
    const result = await makeJiraRequest(`/issue/${key}/comment`, 'POST', commentBody);
    
    console.log(`JIRA Comment: Successfully added comment to ticket ${key}`);
    return res.json({
      success: true,
      commentId: result.id,
      message: `Comment added to ticket ${key}`,
      comment: comment
    });
    
  } catch (err) {
    console.error("JIRA Comment Error:", err);
    res.status(500).send({ error: err.message });
  }
});

// Health check endpoint
app.get('/healthz', async (req, res) => {
  try {
    // Test JIRA connection by getting server info
    await makeJiraRequest('/serverInfo');
    res.status(200).send('OK - JIRA connection healthy');
  } catch (err) {
    console.error('JIRA Health Check Failed:', err);
    res.status(500).send('JIRA connection failed');
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`JIRA MCP Server running on port ${PORT}`);
  console.log(`JIRA Base URL: ${JIRA_BASE_URL}`);
});
